import {Component, computed, effect, inject, input, Input, OnInit, Signal, signal, WritableSignal} from '@angular/core';
import {MaterialsEditorModule} from '../../../../materials-editor/materials-editor.module';
import {SmartCreationMaterialDetail} from '../../../../smart-creation/models/smart-creation.types';
import { ViewModeEnum} from '../../../../materials-editor/models/material-editor.types';
import {TamApePageType} from '../../../../../models';
import {Tam4SelectorConfig, TamAbstractReduxComponent} from '../../../../../components';
import {DeduplicationSelectors} from '../../../store/deduplication.selectors';
import { SelectorMap } from '@creactives/models';
import {TranslateService} from '@ngx-translate/core';
import {Tam4TranslationService} from '@creactives/tam4-translation-core';
import {Store} from '@ngrx/store';
import {DeduplicationState} from '../../../store/deduplication.state';
import {DeduplicationService} from '../../../services/deduplication.service';
import {InternalCommonModule} from '../../../../common/iternal-common.module';
import { DeduplicationFieldConfiguration} from '../../../models/deduplication.models';
import {DynamicFormInputFactory} from '../../../../materials-editor/service/dynamic-form-input.factory';
import {FormControl, FormGroup} from '@angular/forms';
import {ProgressSpinnerModule} from 'primeng/progressspinner';
import {MessageModule} from 'primeng/message';

interface EnrichmentTableRow {
  fieldId: string;
  label: string;
  primary: DeduplicationFieldConfiguration;
  secondaries: DeduplicationFieldConfiguration[];
  groupTabLabel?: string;
  groupTabKey?: string;
}

interface EnrichmentTableColumn {
  field: string;
  header: string;
  isPrimary: boolean;
  materialKey?: string;
}

interface DynamicInputConfig {
  component: any;
  params: any;
  formControl: FormControl;
  editable: boolean;
}

const storeSelectors: Tam4SelectorConfig[] = [
  {key: 'materials', selector: DeduplicationSelectors.getMaterials},
  {key: 'enrichmentMaterialDetails', selector: DeduplicationSelectors.getEnrichmentMaterialDetails},
];

@Component({
    selector: 'relationship-material-enrich',
    styleUrls: ['./relationship-material-enrich.component.scss'],
    template: `
      <div class="material-enrichment-container">
        <h3>{{ 'deduplication.enrichment.title' | translate }}</h3>

        @if (isLoading()) {
          <p-progressSpinner></p-progressSpinner>
          <p>{{ 'deduplication.enrichment.loading' | translate }}</p>
        } @else if (hasData() && tableRows()?.length > 0 && tableColumns()?.length > 0) {
          <p-table [value]="tableRows()"
                   styleClass="p-datatable-gridlines"
                   [scrollable]="true"
                   scrollHeight="500px"
                   [rowGroupMode]="'subheader'"
                   groupRowsBy="groupTabLabel"
          >
            <ng-template pTemplate="header">
              <tr>
                <th pFrozenColumn></th>
                <th pFrozenColumn>Primary</th>
                <th colspan="{{ tableColumns().length - 2 }}">
                  Secondaries {{ tableColumns().length }}
                </th>
              </tr>
            </ng-template>

            <ng-template pTemplate="groupheader" let-rowData>
              <tr class="p-rowgroup-header">
                <td [attr.colspan]="tableColumns().length">
                  <span class="group-header-label">{{ rowData.groupTabLabel }}</span>
                </td>
              </tr>
            </ng-template>
            <ng-template pTemplate="body" let-row>
              <tr>
                @for (column of tableColumns(); track column.field) {
                    @switch (column.field) {
                      @case ('label') {
                        <td pFrozenColumn>
                            <span >{{ row.label | attributeNameTranslate}}</span>
                        </td>
                      }
                      @case ('primary') {
                        <td pFrozenColumn>
                          @if (getPrimaryInputConfig(row.primary); as inputConfig) {
                            <span scDynamicContainerTemplate
                                  [dynamicComponent]="inputConfig.component"
                                  [dynamicParams]="inputConfig.params"
                                  [editable]="inputConfig.editable && editable"
                                  [viewMode]="type"
                                  [showEmptyValues]="showEmptyValues"
                                  [mandatory]="inputConfig.params?.mandatory"
                                  [coreAttribute]="inputConfig.params?.componentParams?.coreAttribute"
                                  [disableCopy]="true"
                            >
                            </span>
                          } @else {
                            <span class="raw-value">{{ row.primary.value || '' }}</span>
                          }
                        </td>
                      }
                      @default {
                        <td>
                          @if (column.field.startsWith('secondary_')) {
                            @if (getSecondaryForColumn(row, column); as secondaryField) {
                                <p-checkbox
                                    [(ngModel)]="secondaryField.selected"
                                    label="{{ (secondaryField.value || '') | attributeValueTranslate : secondaryField.id }}"
                                    [name]="secondaryField.id"
                                    [disabled]="!secondaryField.editable">
                                </p-checkbox>
                            } @else {
                              <span class="empty-cell"></span>
                            }
                          }
                        </td>
                      }
                    }
                }
              </tr>
            </ng-template>
            <ng-template pTemplate="footer">
              <tr>
                <td colspan="2" pFrozenColumn>
                  Non editables
                  <p-checkbox
                    [binary]="true"
                    inputId="nonEditableCheck" />
                </td>
                <td colspan="{{ tableColumns().length - 2 }}"></td>
              </tr>
            </ng-template>
          </p-table>
        } @else {
          <p-message severity="info"
                     [text]="'deduplication.enrichment.noData' | translate">
          </p-message>
        }
      </div>
    `,
  imports: [
    MaterialsEditorModule,
    InternalCommonModule,
    ProgressSpinnerModule,
    MessageModule,
    // MaterialDetailsEnrichment,
    // MaterialEnrichmentDetailsComponent
  ],
  standalone: true
})
export class RelationshipMaterialEnrich extends TamAbstractReduxComponent<SelectorMap> implements OnInit {

  @Input() mdDomain!: string;

  enrichmentMaterialDetails = computed(() => this.signals?.enrichmentMaterialDetails());

  tableRows = computed(() => this.transformDataToTableRows());
  tableColumns = computed(() => this.generateTableColumns());

  isLoading = computed(() => !this.enrichmentMaterialDetails());
  hasData = computed(() => {
    const data = this.enrichmentMaterialDetails();
    return data && Array.isArray(data) && data.length > 0 &&
           data.some(group => group.rows && group.rows.length > 0);
  });

  type: ViewModeEnum = ViewModeEnum.EDIT;
  page: string = TamApePageType.EDIT;
  categoriesFormGroup: any;

  service = inject(DeduplicationService);
  dynamicFormInputFactory = inject(DynamicFormInputFactory);
  materialDetails: SmartCreationMaterialDetail;

  showEmptyValues = true;
  editable = true;

  constructor(protected translate: TranslateService,
              protected tamTranslate: Tam4TranslationService,
              protected store: Store<DeduplicationState>) {
    super(translate, tamTranslate, store, storeSelectors);

    effect(() => {
      const enrichmentData = this.enrichmentMaterialDetails();
      if (enrichmentData) {
        console.log('Enrichment data updated:', enrichmentData);
        console.log('Table rows:', this.tableRows());
        console.log('Table columns:', this.tableColumns());
      }
    });
  }

  ngOnInit() {
    this.service.loadPrimaryMaterialDetails();
  }

  private transformDataToTableRows(): EnrichmentTableRow[] {
    const data = this.enrichmentMaterialDetails();
    if (!data || !Array.isArray(data)) {
      return [];
    }

    const rows: EnrichmentTableRow[] = [];

    // Process each group in the new structure
    data.forEach(group => {
      if (!group.rows || !Array.isArray(group.rows)) {
        return;
      }

      // Process each row within the group
      group.rows?.filter(r => !r.hidden).forEach(rowData => {
        const fieldId = rowData.id;

        // Get primary field (should have one key)
        const primaryKeys = Object.keys(rowData.primary || {});
        const primaryField = primaryKeys.length > 0 ? rowData.primary[primaryKeys[0]] : null;

        if (!primaryField) {
          return; // Skip if no primary field
        }

        // Get label from descriptionLanguages[0] if available, fallback to field label or id
        const label = primaryField.descriptionLanguages?.[0] || primaryField.label || fieldId;

        // Collect secondary fields for this fieldId
        const secondaries: DeduplicationFieldConfiguration[] = [];
        if (rowData.secondaries) {
          Object.keys(rowData.secondaries).forEach(secondaryMaterialId => {
            const secondaryField = rowData.secondaries[secondaryMaterialId];
            secondaries.push(secondaryField);
          });
        }
        rows.push({
          fieldId,
          label,
          primary: primaryField,
          secondaries,
          groupTabLabel: group.groupTabLabel,
          groupTabKey: group.groupTabKey
        });
      });
    });

    return rows;
  }

  private generateTableColumns(): EnrichmentTableColumn[] {
    const data = this.enrichmentMaterialDetails();
    if (!data || !Array.isArray(data)) {
      return [];
    }

    const columns: EnrichmentTableColumn[] = [
      // Label column
      { field: 'label', header: 'Field', isPrimary: false },
      // Primary column
      { field: 'primary', header: 'Primary', isPrimary: true }
    ];

    // Add secondary columns based on unique secondary material IDs from the new structure
    // Find the first group and first row to get secondary material IDs
    for (const group of data) {
      if (group.rows && group.rows.length > 0) {
        const firstRow = group.rows[0];
        if (firstRow.secondaries) {
          const secondaryMaterialIds = Object.keys(firstRow.secondaries);
          secondaryMaterialIds.forEach((materialId, index) => {
            columns.push({
              field: `secondary_${index}`,
              header: `${materialId} Secondary ${index + 1}`,
              isPrimary: false,
              materialKey: materialId
            });
          });
          break; // Only need to process the first row to get column structure
        }
      }
    }

    console.log(columns);
    return columns;
  }

  /**
   * Create dynamic input configuration for a field
   */
  createDynamicInputConfig(fieldConfig: DeduplicationFieldConfiguration, isEditable: boolean): DynamicInputConfig {
    // Create a form control for this field
    const formControl = new FormControl(fieldConfig.value || '');

    // Create a temporary form group to hold the control
    const tempFormGroup = new FormGroup({
      [fieldConfig.id]: formControl
    });

    // Set editable state
    const editableConfig = { ...fieldConfig, editable: isEditable };

    // Use the factory to create the dynamic component wrapper
    const componentWrapper = this.dynamicFormInputFactory.buildDynamicFormInput(
      editableConfig as any, // Cast to SmartFieldConfiguration
      this.page,
      tempFormGroup,
      0, // sheetIndex
      0, // tabIndex
      this.type,
      null, // initialData
      null  // dynamicAutocompleteFn
    );



    // Create a clean params object without any numeric properties
    const cleanParams = {
      formGroup: tempFormGroup,
      formControlName: fieldConfig.id,
      type: fieldConfig.type,
      editable: isEditable,
      value: fieldConfig.value,
      mandatory: fieldConfig.mandatory,
      coreAttribute: fieldConfig.coreAttribute,
      label: fieldConfig.label,
      hasError: false,
      componentProps: {
        editable: isEditable,
        value: fieldConfig.value
      }
    };

    return {
      component: componentWrapper.component,
      params: cleanParams,
      formControl,
      editable: isEditable
    };
  }

  /**
   * Get dynamic input configuration for primary field (editable)
   */
  getPrimaryInputConfig(fieldConfig: any): DynamicInputConfig {
    return this.createDynamicInputConfig(fieldConfig, fieldConfig.editable);
  }

  /**
   * Get secondary field for a specific column
   */
  getSecondaryForColumn(row: EnrichmentTableRow, column: EnrichmentTableColumn): DeduplicationFieldConfiguration | null {
    if (!column.field.startsWith('secondary_')) {
      return null;
    }

    const secondaryIndex = parseInt(column.field.replace('secondary_', ''), 10);
    return row.secondaries[secondaryIndex] || null;
  }

}
