import { CommonModule } from '@angular/common';
import {Component, effect, EventEmitter, inject, Input, OnInit, Output, Signal} from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { RelationshipType } from 'src/app/modules/relationships/endpoint/relationships.model';
import {CreateDeduplicationRequest, Subtype, SubtypesRelationships} from '../../../models/deduplication.models';
import { DeduplicationService } from "../../../services/deduplication.service";
import { RelationshipTypeComponent } from './relationship-type.component';
import {RelationshipMaterialEnrich} from './relationship-material-enrich.component';
import {Tam4SelectorConfig, TamAbstractReduxComponent} from '../../../../../components';
import {DeduplicationSelectors} from '../../../store/deduplication.selectors';
import {SelectorMap} from '@creactives/models';
import {Tam4TranslationService} from '@creactives/tam4-translation-core';
import {Store} from '@ngrx/store';
const storeSelectors: Tam4SelectorConfig[] = [
    {key: 'materials', selector: DeduplicationSelectors.getMaterials},
    {key: 'enrichmentMaterialDetails', selector: DeduplicationSelectors.getEnrichmentMaterialDetails},
];
@Component({
             selector: 'relationship-types-list',
             template: `
              <div>
                <div class="title">
                  <strong>{{('layout.relationship-popup.choose-relationship-types' | lowercase) | translate}}</strong>
                </div>

                  @if (signals?.materials() && signals?.materials()?.length > 0) {
                      <!-- FAKE COMPONENT -->
                      <relationship-material-enrich [mdDomain]="mdDomain">
                      </relationship-material-enrich>
                  }
                <!-- <relationship-type *ngFor="let relType of relationshipTypes"
                                          [label]="relType.value + '-label'"
                                          [value]="relType.value"
                                          [mdDomain]="mdDomain"
                ></relationship-type> -->

                @for(relType of subtypesRelationships(); track relType) {
                    <div relationshipTypeConfig
                          [label]="relType.relationshipType + '-label'"
                          [value]="relType.relationshipType"
                          [mdDomain]="mdDomain"
                          [subtypesRelationship] = "relType?.subtypes"
                          [deduplicationRequest] = "deduplicationRequest"
                          (selectedSubtypesRelationships)="onSubtypeSelected($event)">
                    ></div>
                }

              </div>
             `,
             styles: [
              `
               .title {
                   padding-left: 20px;
               }
              `
             ],
             standalone: true,
    imports: [
        CommonModule,
        TranslateModule,
        RelationshipTypeComponent,
        RelationshipMaterialEnrich
    ]
})
export class RelationshipTypesListComponent extends TamAbstractReduxComponent<SelectorMap>  implements OnInit {

  @Input() mdDomain: string;
  relationshipTypes: Array<{ value: RelationshipType }>;
  @Input() subtypesRelationships: Signal<SubtypesRelationships[] | []>;
  @Input() selectedSubtypeRelationship: Signal<Subtype | null>;
  @Input() deduplicationRequest: Signal<CreateDeduplicationRequest | null>;
  @Output() selectedSubtypes = new EventEmitter<any>();

  service = inject(DeduplicationService);

    constructor(
        protected translate: TranslateService,
        protected tamTranslate: Tam4TranslationService,
        protected store: Store
    ) {
        super(translate, tamTranslate, store, storeSelectors);
    }
  ngOnInit() {
  }


  onSubtypeSelected(selected: any) {
   this.service.action_selectedSubtypesRelationship(selected);
  }

}
